import { Injectable } from '@nestjs/common';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { PrismaService } from '../prisma/prisma.service';
import * as fs from 'fs';
import * as path from 'path';
import { HttpError } from 'src/common/exception/http.error';

@Injectable()
export class RoomsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreateRoomDto, files: Express.Multer.File[]) {
    const category = await this.prisma.category.findUnique({
      where: { id: dto.categoryId },
    });
    if (!category) {
      throw HttpError({ code: 'Category not found' });
    }
    if (files.length > 0) {
      const imageUrls: string[] = [];
      for (const file of files) {
        const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1e9);
        const filename = uniqueName + path.extname(file.originalname);
        const uploadPath = path.join(
          process.cwd(),
          'uploads',
          'rooms',
          filename,
        );

        // uploads/rooms papkasini tekshirib, yo‘q bo‘lsa yaratish
        const dir = path.dirname(uploadPath);
        console.log('Upload directory:', dir);
        console.log('Full upload path:', uploadPath);
        console.log('File buffer size:', file.buffer.length);

        if (!fs.existsSync(dir)) {
          console.log('Creating directory:', dir);
          fs.mkdirSync(dir, { recursive: true });
        }

        try {
          // Faylni diskka yozish
          fs.writeFileSync(uploadPath, file.buffer);
          console.log('File successfully saved:', filename);
          console.log('File exists after write:', fs.existsSync(uploadPath));
        } catch (error) {
          console.error('Error writing file:', error);
          throw new Error(`Failed to save file: ${filename}`);
        }

        imageUrls.push(`/uploads/rooms/${filename}`);
      }
      const room = await this.prisma.room.create({
        data: {
          title: dto.title,
          description: dto.description,
          price: dto.price,
          categoryId: dto.categoryId,
          images: {
            create: imageUrls.map((url) => ({ url })),
          },
        },
        include: {
          images: true,
        },
      });
      return room;
    }
  }

  findAll() {
    return `This action returns all rooms`;
  }

  findOne(id: number) {
    return `This action returns a #${id} room`;
  }

  update(id: number, updateRoomDto: UpdateRoomDto) {
    return `This action updates a #${id} room`;
  }

  remove(id: number) {
    return `This action removes a #${id} room`;
  }
}
