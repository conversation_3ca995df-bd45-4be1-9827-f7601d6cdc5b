import { Injectable } from '@nestjs/common';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { PrismaService } from '../prisma/prisma.service';
import * as fs from 'fs';
import * as path from 'path';
import { HttpError } from 'src/common/exception/http.error';

@Injectable()
export class RoomsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreateRoomDto, files: Express.Multer.File[]) {
    const category = await this.prisma.category.findUnique({
      where: { id: dto.categoryId },
    });
    if (!category) {
      throw HttpError({ code: 'Category not found' });
    }
    if (files.length > 0) {
      const imageUrls: string[] = [];
      for (const file of files) {
        const uniqueName =
          Date.now() + '-' + Math.round(Math.random() * 1e9);
        const filename = uniqueName + path.extname(file.originalname);
        const uploadPath = path.join(__dirname, '../../uploads/rooms', filename);

        // uploads/rooms papkasini tekshirib, yo‘q bo‘lsa yaratish
        const dir = path.dirname(uploadPath);
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        // Faylni diskka yozish
        fs.writeFileSync(uploadPath, file.buffer);
        imageUrls.push(`/uploads/rooms/${filename}`);
      }
      const room = await this.prisma.room.create({
        data: {
          title: dto.title,
          description: dto.description,
          price: dto.price,
          categoryId: dto.categoryId,
          images: {
            create: imageUrls.map((url) => ({ url })),
          },
        },
        include: {
          images: true,
        },
      });
      return room;
    }
  }

  findAll() {
    return `This action returns all rooms`;
  }

  findOne(id: number) {
    return `This action returns a #${id} room`;
  }

  update(id: number, updateRoomDto: UpdateRoomDto) {
    return `This action updates a #${id} room`;
  }

  remove(id: number) {
    return `This action removes a #${id} room`;
  }
}
