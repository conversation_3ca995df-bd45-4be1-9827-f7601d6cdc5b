import { Injectable } from '@nestjs/common';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class RoomsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(dto: CreateRoomDto, imagePaths: string[]) {
    const room = await this.prisma.room.create({
      data: {
        title: dto.title,
        description: dto.description,
        price: dto.price,
        categoryId: dto.categoryId,
        images: {
          create: imagePaths.map((url) => ({ url })),
        },
      },
      include: {
        images: true,
      },
    });
  
    return room;
  }

  findAll() {
    return `This action returns all rooms`;
  }

  findOne(id: number) {
    return `This action returns a #${id} room`;
  }

  update(id: number, updateRoomDto: UpdateRoomDto) {
    return `This action updates a #${id} room`;
  }

  remove(id: number) {
    return `This action removes a #${id} room`;
  }
}
