import { PartialType } from '@nestjs/swagger';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsOptional, IsString } from 'class-validator';
import { CreateAdminDto } from './create-admin.dto';
import { IsName } from 'src/common/dtos/name.dto';
import { IsPassword } from 'src/common/dtos/password.dto';

export class UpdateAdminDto extends PartialType(CreateAdminDto) {
  @IsName(false)
  name?: string;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @IsPassword(false)
  newPassword?: string;

  @IsPassword(false)
  oldPassword?: string;
}
