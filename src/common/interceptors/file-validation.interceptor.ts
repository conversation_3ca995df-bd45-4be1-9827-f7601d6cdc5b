import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  BadRequestException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class FileValidationInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const files = request.files;

    return next.handle().pipe(
      catchError((error) => {
        // Agar xatolik bo'lsa, yuk<PERSON><PERSON> fayllarni tozalash
        this.cleanupFiles(files);
        return throwError(() => error);
      }),
    );
  }

  private cleanupFiles(files: any) {
    if (!files) return;

    // Agar files object bo'lsa (FileFieldsInterceptor)
    if (typeof files === 'object' && !Array.isArray(files)) {
      Object.values(files).forEach((fileArray: any) => {
        if (Array.isArray(fileArray)) {
          fileArray.forEach((file: any) => {
            this.cleanupFile(file);
          });
        }
      });
    }
    // Agar files array bo'lsa (FilesInterceptor)
    else if (Array.isArray(files)) {
      files.forEach((file: any) => {
        this.cleanupFile(file);
      });
    }
    // Agar bitta file bo'lsa (FileInterceptor)
    else if (files) {
      this.cleanupFile(files);
    }
  }

  private cleanupFile(file: any) {
    if (!file) return;

    try {
      // Agar diskStorage ishlatilgan bo'lsa (file.path mavjud)
      if (file.path && fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
        console.log('Deleted file from disk:', file.path);
      }
      // Agar memoryStorage ishlatilgan bo'lsa (file.buffer mavjud)
      else if (file.buffer) {
        // Memory storage da fayllar avtomatik tozalanadi
        // Lekin buffer'ni null qilib qo'yishimiz mumkin
        file.buffer = null;
        console.log('Cleared file buffer from memory:', file.originalname);
      }
    } catch (error) {
      console.error(
        'Error cleaning up file:',
        file.originalname || 'unknown',
        error,
      );
    }
  }
}
