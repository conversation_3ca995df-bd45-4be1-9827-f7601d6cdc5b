import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  BadRequestException,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class FileValidationInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const files = request.files;

    return next.handle().pipe(
      catchError((error) => {
        // Agar xatolik bo'lsa, yuk<PERSON><PERSON> fayllarni o'chirish
        this.cleanupFiles(files);
        return throwError(() => error);
      }),
    );
  }

  private cleanupFiles(files: any) {
    if (!files) return;

    // Agar files object bo'lsa (FileFieldsInterceptor)
    if (typeof files === 'object' && !Array.isArray(files)) {
      Object.values(files).forEach((fileArray: any) => {
        if (Array.isArray(fileArray)) {
          fileArray.forEach((file: any) => {
            this.deleteFile(file.path);
          });
        }
      });
    }
    // Agar files array bo'lsa (FilesInterceptor)
    else if (Array.isArray(files)) {
      files.forEach((file: any) => {
        this.deleteFile(file.path);
      });
    }
    // Agar bitta file bo'lsa (FileInterceptor)
    else if (files && files.path) {
      this.deleteFile(files.path);
    }
  }

  private deleteFile(filePath: string) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } catch (error) {
      console.error('Error deleting file:', filePath, error);
    }
  }
}
