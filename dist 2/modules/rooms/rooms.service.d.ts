import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { PrismaService } from '../prisma/prisma.service';
export declare class RoomsService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    create(dto: CreateRoomDto, files: Express.Multer.File[]): Promise<{
        images: {
            id: number;
            url: string;
            roomId: number;
        }[];
    } & {
        id: number;
        title: string;
        description: string;
        price: number;
        categoryId: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findAll(): string;
    findOne(id: number): string;
    update(id: number, updateRoomDto: UpdateRoomDto): string;
    remove(id: number): string;
}
