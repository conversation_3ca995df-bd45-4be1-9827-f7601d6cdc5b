"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const fs = require("fs");
const path = require("path");
const http_error_1 = require("../../common/exception/http.error");
let RoomsService = class RoomsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async create(dto, files) {
        const category = await this.prisma.category.findUnique({
            where: { id: dto.categoryId },
        });
        if (!category) {
            throw (0, http_error_1.HttpError)({ code: 'Category not found' });
        }
        if (files.length > 0) {
            const imageUrls = [];
            for (const file of files) {
                const uniqueName = Date.now() + '-' + Math.round(Math.random() * 1e9);
                const filename = uniqueName + path.extname(file.originalname);
                const uploadPath = path.join(__dirname, '../../uploads/rooms', filename);
                const dir = path.dirname(uploadPath);
                if (!fs.existsSync(dir)) {
                    fs.mkdirSync(dir, { recursive: true });
                }
                fs.writeFileSync(uploadPath, file.buffer);
                console.log(filename);
                imageUrls.push(`/uploads/rooms/${filename}`);
            }
            const room = await this.prisma.room.create({
                data: {
                    title: dto.title,
                    description: dto.description,
                    price: dto.price,
                    categoryId: dto.categoryId,
                    images: {
                        create: imageUrls.map((url) => ({ url })),
                    },
                },
                include: {
                    images: true,
                },
            });
            return room;
        }
    }
    findAll() {
        return `This action returns all rooms`;
    }
    findOne(id) {
        return `This action returns a #${id} room`;
    }
    update(id, updateRoomDto) {
        return `This action updates a #${id} room`;
    }
    remove(id) {
        return `This action removes a #${id} room`;
    }
};
exports.RoomsService = RoomsService;
exports.RoomsService = RoomsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RoomsService);
//# sourceMappingURL=rooms.service.js.map