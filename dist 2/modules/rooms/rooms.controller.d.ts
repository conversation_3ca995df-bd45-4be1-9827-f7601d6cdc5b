import { RoomsService } from './rooms.service';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
export declare class RoomsController {
    private readonly roomsService;
    constructor(roomsService: RoomsService);
    create(createFormDto: CreateRoomDto, files: Express.Multer.File[]): Promise<{
        images: {
            id: number;
            url: string;
            roomId: number;
        }[];
    } & {
        id: number;
        title: string;
        description: string;
        price: number;
        categoryId: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    findAll(): string;
    findOne(id: string): string;
    update(id: string, updateRoomDto: UpdateRoomDto): string;
    remove(id: string): string;
}
