"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAdminDto = void 0;
const name_dto_1 = require("../../../common/dtos/name.dto");
const password_dto_1 = require("../../../common/dtos/password.dto");
class UpdateAdminDto {
}
exports.UpdateAdminDto = UpdateAdminDto;
__decorate([
    (0, name_dto_1.IsName)(false),
    __metadata("design:type", String)
], UpdateAdminDto.prototype, "name", void 0);
__decorate([
    (0, password_dto_1.IsPassword)(false),
    __metadata("design:type", String)
], UpdateAdminDto.prototype, "newPassword", void 0);
__decorate([
    (0, password_dto_1.IsPassword)(false),
    __metadata("design:type", String)
], UpdateAdminDto.prototype, "oldPassword", void 0);
//# sourceMappingURL=update-admin.dto.js.map