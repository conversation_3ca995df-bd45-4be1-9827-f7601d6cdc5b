"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileValidationInterceptor = void 0;
const common_1 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
const operators_1 = require("rxjs/operators");
const fs = require("fs");
let FileValidationInterceptor = class FileValidationInterceptor {
    intercept(context, next) {
        const request = context.switchToHttp().getRequest();
        const files = request.files;
        return next.handle().pipe((0, operators_1.catchError)((error) => {
            this.cleanupFiles(files);
            return (0, rxjs_1.throwError)(() => error);
        }));
    }
    cleanupFiles(files) {
        if (!files)
            return;
        if (typeof files === 'object' && !Array.isArray(files)) {
            Object.values(files).forEach((fileArray) => {
                if (Array.isArray(fileArray)) {
                    fileArray.forEach((file) => {
                        this.cleanupFile(file);
                    });
                }
            });
        }
        else if (Array.isArray(files)) {
            files.forEach((file) => {
                this.cleanupFile(file);
            });
        }
        else if (files) {
            this.cleanupFile(files);
        }
    }
    cleanupFile(file) {
        if (!file)
            return;
        try {
            if (file.path && fs.existsSync(file.path)) {
                fs.unlinkSync(file.path);
                console.log('Deleted file from disk:', file.path);
            }
            else if (file.buffer) {
                file.buffer = null;
                console.log('Cleared file buffer from memory:', file.originalname);
            }
        }
        catch (error) {
            console.error('Error cleaning up file:', file.originalname || 'unknown', error);
        }
    }
};
exports.FileValidationInterceptor = FileValidationInterceptor;
exports.FileValidationInterceptor = FileValidationInterceptor = __decorate([
    (0, common_1.Injectable)()
], FileValidationInterceptor);
//# sourceMappingURL=file-validation.interceptor.js.map