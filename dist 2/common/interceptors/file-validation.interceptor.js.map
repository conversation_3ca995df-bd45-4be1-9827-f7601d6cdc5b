{"version": 3, "file": "file-validation.interceptor.js", "sourceRoot": "", "sources": ["../../../src/common/interceptors/file-validation.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAMwB;AACxB,+BAA8C;AAC9C,8CAA4C;AAC5C,yBAAyB;AAIlB,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC,UAAU,EAAE,CAAC;QACpD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAE5B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,sBAAU,EAAC,CAAC,KAAK,EAAE,EAAE;YAEnB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YACzB,OAAO,IAAA,iBAAU,EAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,YAAY,CAAC,KAAU;QAC7B,IAAI,CAAC,KAAK;YAAE,OAAO;QAGnB,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,SAAc,EAAE,EAAE;gBAC9C,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;wBAC9B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;oBACzB,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;aAEI,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YAC9B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACL,CAAC;aAEI,IAAI,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,IAAS;QAC3B,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,IAAI,CAAC;YAEH,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC1C,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC;iBAEI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAGrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;gBACnB,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CACX,yBAAyB,EACzB,IAAI,CAAC,YAAY,IAAI,SAAS,EAC9B,KAAK,CACN,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAA;AA/DY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CA+DrC"}