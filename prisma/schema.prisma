datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Admin {
  id        Int      @id @default(autoincrement())
  email     String   @unique
  password  String
  name      String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Category {
  id        Int     @id @default(autoincrement())
  name      String
  rooms     Room[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Room {
  id          Int      @id @default(autoincrement())
  title       String
  description String
  price       Float
  categoryId  Int
  category    Category @relation(fields: [categoryId], references: [id])
  images      Image[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Image {
  id      Int    @id @default(autoincrement())
  url     String
  roomId  Int
  room    Room   @relation(fields: [roomId], references: [id])
}

