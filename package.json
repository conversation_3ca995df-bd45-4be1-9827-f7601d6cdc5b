{"name": "projectname", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "build": "nest build", "prod": "node dist/main", "format": "prettier --write \"src/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "typos": "typos"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/serve-static": "^4.0.2", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "@prisma/client": "^6.8.2", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "dotenv": "^16.4.7", "envalid": "^8.0.0", "helmet": "^6.0.0", "jsonwebtoken": "^8.5.1", "mysql2": "^3.11.5", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "swagger-ui-express": "^4.6.0", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/crypto-js": "^4.1.1", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^8.5.9", "@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}